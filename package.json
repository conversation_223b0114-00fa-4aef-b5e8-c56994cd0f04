{"name": "ai-proxy", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@ai-sdk/openai-compatible": "^0.2.14", "@hono/node-server": "^1.14.4", "@hono/zod-validator": "^0.7.0", "@t3-oss/env-core": "^0.13.8", "ai": "^4.3.16", "hono": "^4.7.11", "zod": "^3.25.57"}, "devDependencies": {"@types/node": "^20.11.17", "prettier": "^3.5.3", "tsx": "^4.7.1", "typescript": "^5.8.3"}}