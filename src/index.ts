import { serve } from '@hono/node-server'
import { Hono } from 'hono'
import { logger } from 'hono/logger'
import {z<PERSON><PERSON><PERSON><PERSON>} from '@hono/zod-validator'
import {z} from 'zod'
import type { Message } from 'ai'

import { env } from './env.js'

const app = new Hono()
app.use(logger())

app.post('/chat', zValidator('json', z.object({
  messages: 
})), async c => {
  return c.text('Hello Hono!')
})

const server = serve(
  {
    fetch: app.fetch,
    port: env.PORT,
  },
  info => {
    console.log(`Server is running on http://localhost:${info.port}`)
  }
)

// graceful shutdown
process.on('SIGINT', () => {
  server.close()
  process.exit(0)
})
process.on('SIGTERM', () => {
  server.close(err => {
    if (err) {
      console.error(err)
      process.exit(1)
    }
    process.exit(0)
  })
})
